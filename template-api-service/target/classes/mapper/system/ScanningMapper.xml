<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.ScanningMapper">
    <resultMap id="scanningMap" type="ScanningResponse">
        <id column="id" property="id"/>
        <result column="default_flag" property="defaultFlag"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <collection property="scanningInfoList" select="scanningInfoQuery" column="scanningId=id" ofType="scanningInfo" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Scanning">
        <id column="id" property="id"/>
        <result column="default_flag" property="defaultFlag"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `default_flag`, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="scanningInfoQuery" resultType="scanningInfo">
        SELECT
            *
        FROM
            t_scanning_info AS si
        WHERE
            si.scanning_id = #{scanningId}
    </select>

    <select id="listByPage" resultMap="scanningMap">
        SELECT
            s.id,
            s.`name`,
            s.`default_flag`,
            s.`status`,
            s.create_time
        FROM
            t_scanning AS s
        <where>
            <if test="status != null and status != ''">
                FIND_IN_SET(s.`status`, #{status})
            </if>
            <if test="keyword != null and keyword != ''">
                AND s.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY
            s.default_flag DESC ,s.create_time DESC , s.id
    </select>

    <select id="details" resultMap="scanningMap">
        SELECT
            s.id,
            s.`name`,
            s.`default_flag`,
            s.`status`,
            s.create_time
        FROM
            t_scanning AS s
        WHERE
            s.id = #{id}
    </select>

</mapper>
