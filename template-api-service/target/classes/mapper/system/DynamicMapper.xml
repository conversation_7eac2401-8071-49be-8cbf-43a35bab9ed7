<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DynamicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Dynamic">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="title" property="title"/>
        <result column="body" property="body"/>
        <result column="cover_image" property="coverImage"/>
        <result column="images" property="images"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, title, body, cover_image, images, `status`, create_by, create_time,
        update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.dynamic.DynamicResponse">
        SELECT
            d.id,
            d.title,
            d.cover_image,
            d.images,
            d.body,
            d.create_time,
            d.`status`,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            SUM( CASE WHEN dk.type = 1 THEN 1 ELSE 0 END ) AS likeCount,
            SUM( CASE WHEN dk.type = 0 THEN 1 ELSE 0 END ) AS dislikeCount
        FROM
            t_dynamic AS d
            LEFT JOIN t_repertoire AS r ON r.id = d.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = d.theater_id
            LEFT JOIN t_dynamic_kudos AS dk ON dk.dynamic_id = d.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (d.`title` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(d.theater_id, #{theaterId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(d.repertoire_id, #{repertoireId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(d.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        GROUP BY
            d.id
        ORDER BY
            d.create_time DESC
    </select>

    <select id="details" resultType="com.youying.system.domain.dynamic.DynamicResponse">
        SELECT
            d.id,
            d.title,
            d.cover_image,
            d.create_time,
            d.`status`,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            d.images,
            d.body
        FROM
            t_dynamic AS d
            LEFT JOIN t_repertoire AS r ON r.id = d.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = d.theater_id
        WHERE
            d.id = #{id}
    </select>

</mapper>
