<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RankMedalInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RankMedalInfo">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="rank_medal_id" property="rankMedalId"/>
        <result column="name" property="name"/>
        <result column="expense_price" property="expensePrice"/>
        <result column="expense_number" property="expenseNumber"/>
        <result column="color" property="color"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, rank_medal_id, `name`, expense_price, expense_number, color
    </sql>

</mapper>
