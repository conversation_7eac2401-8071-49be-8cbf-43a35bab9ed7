<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireInfoMapper">

    <resultMap id="repertoireInfoMap" type="RepertoireInfoResponse">
        <id column="id" property="id"/>
        <collection property="repertoireInfoDetailList" select="queryRepertoireInfoDetails" column="id" ofType="RepertoireInfoDetail" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireInfo">
        <id column="id" property="id"/>
        <result column="prov_id" property="provId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, prov_id, theater_id, repertoire_id, sort, `status`, audit, create_by, create_time, update_by,
        update_time
    </sql>

    <select id="queryRepertoireInfoDetails" resultType="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        SELECT
            start_time,
            end_time
        FROM
            t_repertoire_info_detail
        WHERE
            repertoire_info_id = #{id}
        ORDER BY start_time
    </select>

    <select id="listByPage" resultMap="repertoireInfoMap">
        SELECT
            ri.id,
            ri.prov_id,
            ri.theater_id,
            t.`name` AS theaterName,
            ri.repertoire_id,
            r.`name` AS repertoireName,
            ri.sort,
            ri.`status`,
            ri.audit,
            ri.create_by,
            m.merchant_name AS merchantName,
            ri.create_time
        FROM
            t_repertoire_info AS ri
            LEFT JOIN t_theater AS t ON t.id = ri.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = ri.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = r.merchant_id
        <where>
            t.deleted = '1'
            AND r.deleted = '1'
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(ri.theater_id, #{theaterId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(ri.repertoire_id, #{repertoireId})
            </if>
            <if test="status != null and status != ''">
                AND FIND_IN_SET(ri.status, #{status})
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND
                (
                FIND_IN_SET(ri.initiate_merchant_id, #{merchantId})
                OR
                FIND_IN_SET(ri.release_merchant_id, #{merchantId})
                )
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ri.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="showTime.beginTime != null and showTime.endTime != null">
                AND ( SELECT COUNT(1) FROM t_repertoire_info_detail WHERE t_repertoire_info_detail.repertoire_info_id = ri.id AND DATE(t_repertoire_info_detail.start_time) BETWEEN #{showTime.beginTime} AND #{showTime.endTime} ) > 0
            </if>
        </where>
        ORDER BY ri.sort , ri.create_time DESC
    </select>

</mapper>
