<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserMessageMapper">

    <resultMap id="detailsMap" type="UserMessageResponse">
        <id column="id" property="id"/>
        <collection property="userMessageInfoList" select="userMessageInfoQuery" column="id" ofType="UserMessageResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserMessage">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="user_id" property="userId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="userMessageInfoQuery" resultType="UserMessageResponse">
        SELECT
            umi.body,
            umi.user_id,
            umi.user_merchant_id,
            umi.create_time
        FROM
            t_user_message_info AS umi
        WHERE
            umi.user_message_id = #{id}
        ORDER BY
            umi.create_time
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, user_id, create_by, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.usermessage.UserMessageResponse">
        SELECT
            um.id,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            um.user_id AS userId,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            u.`name` AS userName,
            u.phone,
            u.avatar AS userAvatar,
            (
            SELECT
                create_time
            FROM
                t_user_treasure AS ut
            WHERE
                ut.user_id = um.user_id
                AND ( um.repertoire_id > 0 AND ut.repertoire_id = um.repertoire_id )
                OR ( um.theater_id > 0 AND ut.theater_id = um.theater_id )
                LIMIT 1
            ) AS treasureTime,
            ( SELECT create_time FROM t_user_message_info WHERE user_message_id = um.id AND user_id > 0 ORDER BY create_time DESC LIMIT 1 ) AS userLastTime
        FROM
            t_user_message AS um
            LEFT JOIN t_user AS u ON u.id = um.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_repertoire AS r ON r.id = um.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = um.theater_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (u.`phone` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(um.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(um.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            um.update_time DESC
    </select>

    <select id="details" resultMap="detailsMap">
        SELECT
            um.id,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            um.user_id AS userId,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            (SELECT create_time FROM t_user_treasure WHERE (um.repertoire_id = repertoire_id OR um.theater_id = theater_id) LIMIT 1) AS treasureTime,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            um.repertoire_id,
            um.theater_id,
            um.merchant_id
        FROM
            t_user_message AS um
            LEFT JOIN t_user AS u ON u.id = um.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_repertoire AS r ON r.id = um.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = um.theater_id
        WHERE
            um.id = #{id}
    </select>

</mapper>
