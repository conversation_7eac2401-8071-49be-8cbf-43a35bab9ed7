<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserTicketGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userTicketGroupMap" type="userTicketGroupResponse">
        <id column="id" property="id"/>
        <result column="userId" property="userId"/>
        <collection property="repertoireNameList" select="userTicketGroupQuery" column="{userId=userId,id=id}" ofType="string" />
    </resultMap>

    <select id="userTicketGroupQuery" resultType="string">
        SELECT
            r.`name`
        FROM
            t_user_ticket_group AS utg
            LEFT JOIN t_repertoire AS r ON r.id = utg.repertoire_id
        WHERE
            utg.user_id = #{userId}
            AND utg.ticket_group_id = #{id}
    </select>

    <select id="findUserTicketGroup" resultMap="userTicketGroupMap">
        SELECT
            tg.id,
            tg.`name`,
            #{userId} AS userId,
            (SELECT MAX(create_time) FROM t_user_ticket_group WHERE ticket_group_id = tg.id AND user_id = #{userId}) AS updateTime
        FROM
            t_ticket_group AS tg
        WHERE
            tg.type = 0 OR tg.user_id = #{userId}
        ORDER BY
            tg.type,
            tg.sort,
            tg.create_time DESC,
            tg.id
    </select>

</mapper>
