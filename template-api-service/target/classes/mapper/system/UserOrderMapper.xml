<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserOrder">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="user_id" property="userId"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="order_no" property="orderNo"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="pay_price" property="payPrice"/>
        <result column="refund_price" property="refundPrice"/>
        <result column="pay_status" property="payStatus"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="pay_time" property="payTime"/>
        <result column="refund_time" property="refundTime"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="relation_id" property="relationId"/>
        <result column="badge_type" property="badgeType"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, user_id, transaction_id, order_no, prepay_id, pay_price, refund_price, pay_status, refund_status, pay_time, refund_time, user_receiving_records_id, theater_id, repertoire_id, relation_id, badge_type, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.userorder.UserOrderResponse">
        SELECT
            uo.id,
            uo.order_no,
            u.`name` AS userName,
            u.phone,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            uo.pay_price,
            uo.create_time,
            uo.pay_status,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            urr.price,
            urr.time
        FROM
            t_user_order AS uo
            LEFT JOIN t_theater AS t ON t.id = uo.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = uo.repertoire_id
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = uo.user_receiving_records_id
            LEFT JOIN t_user AS u ON u.id = uo.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
        <where>
            <if test="merchantId != null and merchantId != null">
                AND FIND_IN_SET(uo.merchant_id, #{merchantId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(uo.pay_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (uo.order_no LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY uo.pay_time DESC
    </select>

    <select id="details" resultType="UserOrderResponse">
        SELECT
            uo.id,
            uo.order_no,
            u.`name` AS userName,
            u.phone,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            uo.pay_price,
            uo.pay_time,
            uo.transaction_id,
            rmi.create_time,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            (SELECT image FROM t_user_receiving_records WHERE order_no = uo.`order_no` AND badge_type = 2 LIMIT 1) AS digitalAvatarImage,
            rt.cover_front,
            rt.cover_reverse,
            rt.common_image
        FROM
            t_user_order AS uo
            LEFT JOIN t_user AS u ON u.id = uo.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_repertoire AS r ON r.id = uo.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = uo.theater_id
            LEFT JOIN t_repertoire_ticket AS rt on rt.id = uo.relation_id AND uo.badge_type = 1
        WHERE
            uo.id = #{id}
    </select>

    <select id="findOrderSumPrice" resultType="java.math.BigDecimal">
        SELECT
            IFNULL( SUM( pay_price ), 0 ) AS price
        FROM
            t_user_order AS uo
        WHERE
            DATE(uo.pay_time) BETWEEN #{time.beginTime} AND #{time.endTime}
    </select>

    <select id="export" resultType="com.youying.system.domain.userorder.UserOrderEx">
        SELECT
            uo.order_no,
            u.`name` AS userName,
            u.phone,
            uo.pay_price,
            uo.create_time,
            uo.pay_status,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            urr.time,
            urr.amount
        FROM
            t_user_order AS uo
            LEFT JOIN t_user AS u ON u.id = uo.user_id
            LEFT JOIN t_theater AS t ON t.id = uo.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = uo.repertoire_id
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = uo.user_receiving_records_id
        <where>
            <if test="merchantId != null and merchantId != null">
                AND FIND_IN_SET(uo.merchant_id, #{merchantId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(uo.pay_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (uo.order_no LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY uo.pay_time DESC
    </select>

</mapper>
