<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.LeaderboardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="repertoireLeaderboardMap" type="leaderboardResponse">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <collection property="leaderboardList" select="leaderboardCountQuery" column="id" ofType="leaderboardVO" />
        <collection property="ticketGroupList" select="ticketGroupCountQuery" column="id" ofType="leaderboardVO" />
    </resultMap>

    <select id="leaderboardCountQuery" resultType="leaderboardVO">
        SELECT
            l.id,
            l.`name`,
            IFNULL(ul.`count`,0) `count`
        FROM
            t_leaderboard AS l
                LEFT JOIN (
                SELECT
                    ul.leaderboard_id,
                    COUNT( 1 ) AS `count`
                FROM
                    t_user_leaderboard AS ul
                WHERE
                    ul.type = '0'
                  AND ul.repertoire_id = #{id}
                GROUP BY
                    ul.leaderboard_id) AS ul ON ul.leaderboard_id = l.id
        WHERE
            l.type = '0'
        ORDER BY
            l.type,
            l.sort ,
            l.create_time DESC,
            l.id
    </select>

    <select id="ticketGroupCountQuery" resultType="leaderboardVO">
        SELECT
            tg.id,
            tg.`name`,
            IFNULL(utg.`count`,0) `count`
        FROM
            t_ticket_group AS tg
                LEFT JOIN (
                SELECT
                    utg.ticket_group_id,
                    COUNT( 1 ) AS `count`
                FROM
                    t_user_ticket_group AS utg
                WHERE
                    utg.type = '0'
                  AND utg.repertoire_id = #{id}
                GROUP BY
                    utg.ticket_group_id) AS utg ON utg.ticket_group_id = tg.id
        WHERE
            tg.type = '0'
        ORDER BY
            tg.type,
            tg.sort ,
            tg.create_time DESC,
            tg.id
    </select>

    <select id="listByPage" resultType="com.youying.common.core.domain.entity.Leaderboard">
        SELECT
            *
        FROM
            t_leaderboard
        <where>
            type = 0
            <if test="keyword != null and keyword != ''">
                AND `name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY sort , create_time DESC , id
    </select>

    <select id="findRepertoireLeaderboard" resultMap="repertoireLeaderboardMap">
        SELECT
            r.id,
            r.`name`,
            ( SELECT COUNT( 1 ) FROM t_user_leaderboard AS ul WHERE ul.leaderboard_id = #{id} AND ul.repertoire_id = r.id AND ul.type = '0' ) AS leaderboard_count,
            ( SELECT COUNT( 1 ) FROM t_user_ticket_group AS utg WHERE utg.ticket_group_id = #{id} AND utg.repertoire_id = r.id AND utg.type = '0' ) AS ticket_group_count
        FROM
            t_repertoire AS r
        <where>
            r.deleted = 1
            AND r.`status` = 1
            <if test="keyword != null and keyword != ''">
                AND r.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
    </select>

</mapper>
