<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.TheaterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Theater">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="prov_id" property="provId"/>
        <result column="city_id" property="cityId"/>
        <result column="area_id" property="areaId"/>
        <result column="address" property="address"/>
        <result column="cover_picture" property="coverPicture"/>
        <result column="pictures" property="pictures"/>
        <result column="good_rating_rate" property="goodRatingRate"/>
        <result column="focus_number" property="focusNumber"/>
        <result column="remark" property="remark"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="deleted" property="deleted"/>
        <result column="recommend" property="recommend"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, contact_person, phone, prov_id, city_id, area_id, address, cover_picture, pictures,
        good_rating_rate, focus_number, remark, merchant_id, deleted, recommend, audit, audit_pass_time,
        reasons_rejection, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.theater.TheaterResponse">
        SELECT
            t.id,
            t.`name`,
            m.merchant_name,
            t.focus_number,
            t.qr_code,
            CONCAT_WS( a1.`name`, a2.`name`, a3.`name`, t.address ) AS address,
            t.cover_picture,
            t.create_time,
            t.audit,
            t.reasons_rejection,
            t.recommend,
            t.short_name,
            t.`status`
        FROM
            t_theater AS t
            LEFT JOIN t_merchant AS m ON m.id = t.merchant_id
            LEFT JOIN t_area AS a1 ON a1.id = t.prov_id
            LEFT JOIN t_area AS a2 ON a2.id = t.city_id
            LEFT JOIN t_area AS a3 ON a3.id = t.area_id
        <where>
            t.deleted = '1'
            <if test="keyword != null and keyword != ''">
                AND t.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(t.merchant_id, #{merchantId})
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(t.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(t.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="showTime.beginTime != null and showTime.endTime != null">
                AND ( SELECT COUNT(1) FROM t_repertoire_info_detail WHERE t_repertoire_info_detail.theater_id = t.id AND DATE(t_repertoire_info_detail.start_time) BETWEEN #{showTime.beginTime} AND #{showTime.endTime} ) > 0
            </if>
        </where>
        ORDER BY
            t.recommend DESC , t.create_time DESC , t.id
    </select>

    <select id="findTheaterInfo" resultType="com.youying.system.domain.theater.TheaterResponse">
        SELECT
            t.id,
            t.cover_picture,
            t.pictures,
            t.`name`,
            t.focus_number,
            t.short_name,
            ( SELECT COUNT( 1 ) FROM t_comment WHERE theater_id = t.id AND parent_id = 0 ) AS commentCount,
            ( SELECT COUNT( 1 ) FROM t_comment WHERE theater_id = t.id AND parent_id > 0 ) AS interactionCount
        FROM
            t_theater AS t
        WHERE
            t.id = #{id}
            AND t.deleted = '1'
    </select>

    <select id="pull" resultType="com.youying.common.core.common.PullResponse">
        SELECT
            t.id,
            t.`name`,
            t.merchant_id
        FROM
            t_theater AS t
        <where>
            t.deleted = 1
            <if test="merchantId != null">
                AND t.merchant_id = #{merchantId}
            </if>
            <if test="audit != null">
                AND t.audit = #{audit}
            </if>
            <if test="status != null">
                AND t.`status` = #{status}
            </if>
        </where>
        ORDER BY t.create_time DESC , t.id
    </select>

    <select id="findTheaterAddCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_theater AS t
        <where>
            t.deleted = 1
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(t.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
    </select>

</mapper>
