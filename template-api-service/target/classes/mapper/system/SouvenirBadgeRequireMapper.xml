<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.SouvenirBadgeRequireMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.SouvenirBadgeRequire">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="souvenir_badge_id" property="souvenirBadgeId"/>
        <result column="rank_medal_id" property="rankMedalId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="repertoire_info_id" property="repertoireInfoId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="look_number" property="lookNumber"/>
        <result column="white_list" property="whiteList"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, souvenir_badge_id, rank_medal_id, repertoire_info_detail_id, repertoire_info_id,
        start_time, end_time, look_number, white_list
    </sql>

</mapper>
