<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireLabel">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="name" property="name"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, `name`, repertoire_id, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.repertoirelabel.RepertoireLabelResponse">
        SELECT
            rl.id,
            rl.name,
            r.`name` AS repertoireName,
            rl.create_time
        FROM
            t_repertoire_label AS rl
            LEFT JOIN t_repertoire AS r ON r.id = rl.repertoire_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (rl.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(rl.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            rl.create_time DESC
    </select>

</mapper>
