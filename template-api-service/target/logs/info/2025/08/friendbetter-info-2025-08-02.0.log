2025-08-02 07:06:08.746 [main] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 17.0.15 on macbookofalan.local with PID 35491 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/digital-collections-admin.jar started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target)
2025-08-02 07:06:08.750 [main] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-02 07:06:10.506 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 07:06:10.507 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-02 07:06:10.564 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 07:06:11.516 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-02 07:06:11.517 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.560 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-02 07:06:11.560 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.703 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-02 07:06:11.704 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.745 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-02 07:06:11.745 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.783 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-02 07:06:11.783 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.795 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-02 07:06:11.795 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.913 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-02 07:06:11.913 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:11.963 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-02 07:06:11.963 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:12.006 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-02 07:06:12.006 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:12.050 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-02 07:06:12.050 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:12.076 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-02 07:06:12.076 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:12.126 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-02 07:06:12.126 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:12.164 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-02 07:06:12.164 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:06:13.814 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 07:06:16.346 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8110 is already in use
2025-08-02 07:06:16.351 [main] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-02 07:06:16.351 [main] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-02 07:06:16.357 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-02 07:06:16.360 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-02 07:06:16.477 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-02 07:06:16.493 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8110 was already in use.

Action:

Identify and stop the process that's listening on port 8110 or configure this application to listen on another port.

