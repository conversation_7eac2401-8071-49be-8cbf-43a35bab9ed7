2025-08-02 07:32:31.890 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 17.0.15 on macbookofalan.local with PID 42788 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/digital-collections-admin.jar started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target)
2025-08-02 07:32:31.892 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-02 07:32:33.470 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 07:32:33.470 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-02 07:32:33.495 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 07:32:34.284 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-02 07:32:34.285 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.333 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-02 07:32:34.333 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.457 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-02 07:32:34.458 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.510 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-02 07:32:34.510 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.538 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-02 07:32:34.539 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.550 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-02 07:32:34.550 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.638 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-02 07:32:34.638 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.666 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-02 07:32:34.666 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.693 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-02 07:32:34.693 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.727 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-02 07:32:34.727 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.738 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-02 07:32:34.738 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.764 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-02 07:32:34.764 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:34.783 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-02 07:32:34.783 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:32:41.442 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 07:32:43.931 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 12.233 seconds (JVM running for 12.491)
2025-08-02 07:32:43.934 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-02 07:32:43.935 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-02 07:32:43.935 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-02 07:32:43.935 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-02 07:32:43.935 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-02 07:33:38.968 [SpringApplicationShutdownHook] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-02 07:33:38.969 [SpringApplicationShutdownHook] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-02 07:33:38.981 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-02 07:33:38.986 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
