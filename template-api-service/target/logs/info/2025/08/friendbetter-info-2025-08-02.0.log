2025-08-02 07:41:51.523 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 17.0.15 on macbookofalan.local with PID 46034 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/digital-collections-admin.jar started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target)
2025-08-02 07:41:51.524 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-02 07:41:53.229 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 07:41:53.229 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-02 07:41:53.254 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 07:41:54.203 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-02 07:41:54.203 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.259 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-02 07:41:54.260 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.383 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-02 07:41:54.383 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.418 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-02 07:41:54.418 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.450 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-02 07:41:54.451 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.464 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-02 07:41:54.464 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.562 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-02 07:41:54.562 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.598 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-02 07:41:54.599 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.626 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-02 07:41:54.626 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.653 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-02 07:41:54.653 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.667 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-02 07:41:54.668 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.694 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-02 07:41:54.695 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:54.720 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-02 07:41:54.720 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 07:41:55.658 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 07:41:58.274 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 6.938 seconds (JVM running for 7.215)
2025-08-02 07:41:58.279 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-02 07:41:58.279 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-02 07:41:58.279 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-02 07:41:58.279 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-02 07:41:58.279 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-02 07:42:59.292 [SpringApplicationShutdownHook] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-02 07:42:59.294 [SpringApplicationShutdownHook] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-02 07:42:59.302 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-02 07:42:59.308 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-02 08:07:24.776 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 17.0.15 on macbookofalan.local with PID 48852 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/digital-collections-admin.jar started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target)
2025-08-02 08:07:24.778 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-02 08:07:26.210 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 08:07:26.211 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-02 08:07:26.234 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 08:07:27.137 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-02 08:07:27.137 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.175 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-02 08:07:27.175 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.259 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-02 08:07:27.260 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.297 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-02 08:07:27.298 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.322 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-02 08:07:27.322 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.331 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-02 08:07:27.331 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.404 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-02 08:07:27.404 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.432 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-02 08:07:27.432 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.456 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-02 08:07:27.456 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.482 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-02 08:07:27.482 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.495 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-02 08:07:27.495 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.526 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-02 08:07:27.527 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:27.552 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-02 08:07:27.552 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-02 08:07:28.490 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 08:07:30.891 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 6.299 seconds (JVM running for 6.63)
2025-08-02 08:07:30.895 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-02 08:07:30.895 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-02 08:07:30.895 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-02 08:07:30.895 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-02 08:07:30.895 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-02 08:07:37.288 [SpringApplicationShutdownHook] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-02 08:07:37.289 [SpringApplicationShutdownHook] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-02 08:07:37.299 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-02 08:07:37.305 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
